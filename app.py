import os
import time
import threading
import logging
import asyncio
import json
from typing import AsyncGenerator, Dict, Any
from collections import deque
from dotenv import load_dotenv
from flask import Flask, request, Response, jsonify
from flask_cors import CORS
import fitz  # PyMuPDF
import chromadb
from langchain_chroma import Chroma
from langchain.prompts import PromptTemplate
from langchain.chains import Conversational<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_google_genai import ChatGoogleGenerative<PERSON><PERSON>, GoogleGenerativeAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter

# Load environment variables from .env file
load_dotenv()

# -------------- Logging Setup --------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
LOGGER = logging.getLogger(__name__)

# -------------- Flask App Setup --------------
app = Flask(__name__)
CORS(app)

# -------------- Rate Limiter --------------
class SimpleRateLimiter:
    def __init__(self, max_calls: int, per_secs: int):
        self.max_calls = max_calls
        self.per_secs = per_secs
        self.calls = deque()
        self.lock = threading.Lock()

    def acquire(self):
        with self.lock:
            now = time.time()
            # Remove expired calls
            while self.calls and now - self.calls[0] > self.per_secs:
                self.calls.popleft()

            if len(self.calls) >= self.max_calls:
                # Need to wait until the oldest call expires
                sleep_for = self.per_secs - (now - self.calls[0])
                if sleep_for > 0:
                    LOGGER.info(f"Rate limit hit, sleeping for {sleep_for:.2f}s")
                    time.sleep(sleep_for)
                    now = time.time()
                    while self.calls and now - self.calls[0] > self.per_secs:
                        self.calls.popleft()

            self.calls.append(now)

RATE_LIMITER = SimpleRateLimiter(int(os.getenv("GEMINI_MAX_RPM", "12")), 60)
MAX_CONCURRENCY = int(os.getenv("GEMINI_MAX_CONCURRENCY", "1"))
INFLIGHT = threading.BoundedSemaphore(MAX_CONCURRENCY)

# -------------- Google Gemini Model Setup --------------
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    raise ValueError("Missing GOOGLE_API_KEY")

llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    google_api_key=GOOGLE_API_KEY,
    convert_system_message_to_human=True,
    streaming=True
)

# -------------- ChromaDB Setup --------------
CHROMA_DIR = os.path.join(os.getcwd(), "chroma_store")
os.makedirs(CHROMA_DIR, exist_ok=True)

embedding_fn = GoogleGenerativeAIEmbeddings(
    model="models/embedding-001",
    google_api_key=GOOGLE_API_KEY
)

client = chromadb.PersistentClient(path=CHROMA_DIR)

# Note: The embedding function is passed to Chroma, not to the collection directly
# when using LangChain's Chroma wrapper in this way.
collection = client.get_or_create_collection(
    name="documents"
)

vectorstore = Chroma(
    client=client,
    collection_name="documents",
    embedding_function=embedding_fn
)

retriever = vectorstore.as_retriever(search_kwargs={"k": 3})

# -------------- Prompt Template --------------
prompt_template = """
You are an AI assistant. Use the following context to answer the user question.
If you don’t know, say so honestly.

Context:
{context}

Question:
{question}

Answer:
"""

QA_PROMPT = PromptTemplate(
    input_variables=["context", "question"],
    template=prompt_template
)

qa_chain = ConversationalRetrievalChain.from_llm(
    llm=llm,
    retriever=retriever,
    combine_docs_chain_kwargs={"prompt": QA_PROMPT},
    return_source_documents=True,
)

# -------------- API Routes --------------
@app.route("/api/health")
def health():
    return jsonify({"status": "ok"})

@app.route("/api/upload", methods=["POST"])
def upload_file():
    if "file" not in request.files:
        return jsonify({"error": "No file provided"}), 400

    file = request.files["file"]

    if not file.filename.endswith(".pdf"):
        return jsonify({"error": "Only PDF files are supported"}), 400

    doc = fitz.open(stream=file.read(), filetype="pdf")
    texts = []
    for page in doc:
        texts.append(page.get_text())

    text_content = "\n".join(texts)
    
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200,
    )
    chunks = text_splitter.split_text(text_content)

    # Generate unique IDs for each chunk
    chunk_ids = [f"{file.filename}-{i}" for i, _ in enumerate(chunks)]

    collection.add(
        documents=chunks,
        ids=chunk_ids
    )

    LOGGER.info(f"Indexed document: {file.filename}")
    return jsonify({"message": f"File {file.filename} uploaded and indexed."})


def _stream_answer(user_question: str):
    """Stream answers from the QA chain."""
    RATE_LIMITER.acquire()
    with INFLIGHT:
        LOGGER.info("Retrieving documents and generating response")

        # Use the synchronous streaming method
        for chunk in qa_chain.stream(
            {"question": user_question, "chat_history": []}
        ):
            # Handle different types of chunks from the chain
            if "answer" in chunk:
                answer = chunk["answer"]
                yield f"event: stream\ndata: {json.dumps({'answer': answer})}\n\n"
            elif "source_documents" in chunk:
                docs = chunk["source_documents"]
                doc_metadatas = [
                    {"source": d.metadata.get("source", "unknown")} for d in docs
                ]
                yield f"event: retrieval\ndata: {json.dumps(doc_metadatas)}\n\n"

    yield "event: end\ndata: [DONE]\n\n"


@app.route("/api/stream", methods=["POST"])
def stream_answer():
    data = request.get_json()
    user_question = data.get("question", "")

    if not user_question:
        return jsonify({"error": "No question provided"}), 400

    def generate():
        try:
            for data_chunk in _stream_answer(user_question):
                yield data_chunk
        except Exception as e:
            LOGGER.error(f"Error during streaming: {e}")
            yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"

    return Response(generate(), mimetype="text/event-stream")


# -------------- Main Entrypoint --------------
if __name__ == "__main__":
    LOGGER.info("Starting Flask server on http://127.0.0.1:5000")
    app.run(debug=True, threaded=True)

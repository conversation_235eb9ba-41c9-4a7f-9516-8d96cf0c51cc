# chroma.py
from flask import g
import chromadb

# Local persistent Chroma storage
CHROMA_PERSIST_DIR = "./chroma_store"

# Keys for caching in Flask's g context
CLIENT_G_KEY = "_chroma_client"
COLLECTION_G_PREFIX = "_chroma_collection_"


def get_chroma_client():
    """Return a cached Chroma persistent client or create one if missing."""
    if hasattr(g, CLIENT_G_KEY):
        return getattr(g, CLIENT_G_KEY)

    client = chromadb.PersistentClient(path=CHROMA_PERSIST_DIR)
    setattr(g, CLIENT_G_KEY, client)
    return client


def get_chroma_collection(name: str = "my_collection"):
    """Return a cached Chroma collection or create one if missing."""
    key = COLLECTION_G_PREFIX + name
    if hasattr(g, key):
        return getattr(g, key)

    client = get_chroma_client()
    collection = client.get_or_create_collection(name=name)
    setattr(g, key, collection)
    return collection


def register_chroma_teardown(app):
    """Ensure Chroma client is removed at the end of each request."""
    @app.teardown_appcontext
    def teardown_chroma(exception=None):
        if hasattr(g, CLIENT_G_KEY):
            delattr(g, CLIENT_G_KEY)

        for key in list(g.__dict__.keys()):
            if key.startswith(COLLECTION_G_PREFIX):
                delattr(g, key)
